<?php

    session_start();
    require_once "../dbcontroller.php";
    $db = new DB;

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Mobile Legends Food</title>
    <link rel="stylesheet" href="../bootstrap/css/bootstrap.css">
    <link rel="stylesheet" href="../assets/css/ml-theme.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>
<body>
    <div class="container">
        <div class="row header-ml mb-4">
            <div class="col-12 text-center">
                <h2 class="site-title">
                    <i class="fas fa-crown"></i> ML FOOD ADMIN PANEL
                </h2>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mx-auto">
                <div class="payment-section">
                    <form action="" method="post" class="form-ml">
                        <div class="text-center mb-4">
                            <h3 style="color: var(--ml-gold); text-transform: uppercase; font-weight: 700; text-shadow: 0 0 10px rgba(241, 196, 15, 0.5);">
                                <i class="fas fa-user-shield"></i> Admin Login
                            </h3>
                        </div>

                        <div class="form-group mb-3">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required class="form-control" placeholder="Enter your admin email">
                        </div>

                        <div class="form-group mb-4">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required class="form-control" placeholder="Enter your password">
                        </div>

                        <div class="text-center">
                            <button type="submit" name="login" class="btn btn-ml">
                                <i class="fas fa-sign-in-alt"></i> Access Control Panel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php

    if (isset($_POST['login'])) {
        $email = $_POST['email'];
        $password = hash('sha256', $_POST['password']);

        $sql = "SELECT * FROM tbluser WHERE email='$email' AND password='$password'";

        $count = $db->rowCOUNT($sql);

        if ($count == 0) {
            echo '<div class="alert alert-danger text-center mt-3" style="background-color: var(--ml-red); color: white; border: none; border-radius: 10px;">
                    <i class="fas fa-exclamation-triangle"></i> Email atau Password Salah
                  </div>';
        } else {
            $sql = "SELECT * FROM tbluser WHERE email='$email' AND password='$password'";
            $row = $db->getITEM($sql);

            $_SESSION['user'] = $row['email'];
            $_SESSION['level'] = $row['level'];
            $_SESSION['iduser'] = $row['iduser'];

            header("location:admin.php");
        }
    }

?>
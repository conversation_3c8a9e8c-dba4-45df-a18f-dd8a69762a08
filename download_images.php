<?php
// <PERSON>ript to download sample food images for the restaurant website

// Create upload directory if it doesn't exist
$uploadDir = 'restoran/upload/';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

// Sample food images with direct download URLs from Unsplash
$images = [
    // <PERSON><PERSON><PERSON> (Food) Category
    'burger.jpg' => 'https://images.unsplash.com/photo-1603893662172-99ed0cea2a08?w=400&h=300&fit=crop&crop=center',
    'pasta.jpg' => 'https://images.unsplash.com/photo-1564836235910-c3055ca0f912?w=400&h=300&fit=crop&crop=center',
    'steak.jpg' => 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400&h=300&fit=crop&crop=center',
    'pizza.jpg' => 'https://images.unsplash.com/photo-1654796605349-015a7841f680?w=400&h=300&fit=crop&crop=center',
    'salad.jpg' => 'https://images.unsplash.com/photo-1604908177453-7462950a6a3b?w=400&h=300&fit=crop&crop=center',
    'sandwich.jpg' => 'https://images.unsplash.com/photo-1553909489-cd47e0907980?w=400&h=300&fit=crop&crop=center',
    'rice_bowl.jpg' => 'https://images.unsplash.com/photo-1559847844-5315695dadae?w=400&h=300&fit=crop&crop=center',
    'chicken_curry.jpg' => 'https://images.unsplash.com/photo-1612939675110-fe3a0129a024?w=400&h=300&fit=crop&crop=center',
    'noodles.jpg' => 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop&crop=center',
    'grilled_fish.jpg' => 'https://images.unsplash.com/photo-1598514983318-2f64f8f4796c?w=400&h=300&fit=crop&crop=center',
    
    // Minuman (Drinks) Category
    'coffee.jpg' => 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop&crop=center',
    'juice.jpg' => 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400&h=300&fit=crop&crop=center',
    'smoothie.jpg' => 'https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=400&h=300&fit=crop&crop=center',
    'tea.jpg' => 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400&h=300&fit=crop&crop=center',
    'soda.jpg' => 'https://images.unsplash.com/photo-1581636625402-29b2a704ef13?w=400&h=300&fit=crop&crop=center',
    'milkshake.jpg' => 'https://images.unsplash.com/photo-1572490122747-3968b75cc699?w=400&h=300&fit=crop&crop=center',
    'lemonade.jpg' => 'https://images.unsplash.com/photo-1621263764928-df1444c5e859?w=400&h=300&fit=crop&crop=center',
    'iced_coffee.jpg' => 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=300&fit=crop&crop=center',
    
    // Dessert Category
    'chocolate_cake.jpg' => 'https://images.unsplash.com/photo-1654796605330-8a1248a2cb07?w=400&h=300&fit=crop&crop=center',
    'ice_cream.jpg' => 'https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=400&h=300&fit=crop&crop=center',
    'cheesecake.jpg' => 'https://images.unsplash.com/photo-1533134242443-d4fd215305ad?w=400&h=300&fit=crop&crop=center',
    'tiramisu.jpg' => 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400&h=300&fit=crop&crop=center',
    'fruit_tart.jpg' => 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400&h=300&fit=crop&crop=center',
    'brownie.jpg' => 'https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400&h=300&fit=crop&crop=center',
    'pudding.jpg' => 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400&h=300&fit=crop&crop=center',
    'donut.jpg' => 'https://images.unsplash.com/photo-1551024601-bec78aea704b?w=400&h=300&fit=crop&crop=center',
];

function downloadImage($url, $filename) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $data = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200 && $data !== false) {
        file_put_contents($filename, $data);
        return true;
    }
    return false;
}

echo "<h2>Downloading Sample Food Images...</h2>\n";
echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>\n";

$successCount = 0;
$totalCount = count($images);

foreach ($images as $filename => $url) {
    $filepath = $uploadDir . $filename;
    echo "<p>Downloading: <strong>$filename</strong>... ";
    
    if (downloadImage($url, $filepath)) {
        echo "<span style='color: green;'>✓ Success</span></p>\n";
        $successCount++;
    } else {
        echo "<span style='color: red;'>✗ Failed</span></p>\n";
    }
    
    // Small delay to be respectful to the server
    usleep(500000); // 0.5 seconds
}

echo "</div>\n";
echo "<h3>Download Summary:</h3>\n";
echo "<p><strong>$successCount</strong> out of <strong>$totalCount</strong> images downloaded successfully.</p>\n";

if ($successCount > 0) {
    echo "<h3>Images saved to: <code>$uploadDir</code></h3>\n";
    echo "<p>You can now use these images in your restaurant menu!</p>\n";
}
?>

/* Guzexs Store Theme CSS */
:root {
    /* Color Palette */
    --primary: #3f51b5; /* Indigo */
    --primary-dark: #303f9f;
    --secondary: #212121; /* Dark Gray */
    --accent: #ff5722; /* Deep Orange */
    --light: #f5f5f5; /* Light Gray */
    --dark: #121212;
    --success: #4caf50; /* Green */
    --warning: #ff9800; /* Orange */
    --danger: #f44336; /* Red */
    --info: #2196f3; /* Blue */
    --gray: #9e9e9e;
    --gray-light: #eeeeee;
    --gray-dark: #616161;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #3f51b5, #303f9f);
    --gradient-secondary: linear-gradient(135deg, #212121, #121212);
    --gradient-accent: linear-gradient(135deg, #ff5722, #e64a19);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 15px 35px rgba(0, 0, 0, 0.2);

    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Open Sans', sans-serif;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-rounded: 50px;

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes pulse-glow {
    0% { box-shadow: 0 0 0 0 rgba(63, 81, 181, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(63, 81, 181, 0); }
    100% { box-shadow: 0 0 0 0 rgba(63, 81, 181, 0); }
}

@keyframes shimmer {
    0% { background-position: -100% 0; }
    100% { background-position: 200% 0; }
}

/* Base Styles */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: var(--font-primary);
    color: var(--secondary);
    background-color: var(--light);
    line-height: 1.6;
    scroll-behavior: smooth;
}

/* Hide PHP error messages */
.warning, .error, .notice, .undefined {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    position: absolute !important;
    z-index: -9999 !important;
    overflow: hidden !important;
    pointer-events: none !important;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--secondary);
    line-height: 1.3;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: 1rem;
    color: var(--secondary);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Header */
.header {
    background-color: #4051b5; /* Indigo color from the image */
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

.header.scrolled {
    padding: 0.5rem 0;
    box-shadow: var(--shadow-lg);
    background-color: #3949ab; /* Slightly darker indigo */
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
}

.site-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.site-title a {
    color: white;
    transition: var(--transition-fast);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.site-title a:hover {
    color: var(--light);
    text-decoration: none;
}

/* Navigation */
.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-left: 1.5rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: var(--transition-fast);
}

.nav-link:hover {
    color: white;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: white;
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
}

.user-menu-item {
    margin-left: 1rem;
}

.user-badge {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.15);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-rounded);
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-decoration: none;
}

.user-badge:hover {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.user-badge i {
    margin-right: 0.5rem;
}

.cart-badge {
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ff5722; /* Orange accent color from image */
    color: white;
    font-size: 0.7rem;
    font-weight: 700;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

/* Cart Dropdown */
.cart-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    margin-top: 0.5rem;
    overflow: hidden;
    transition: var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
}

.cart-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.cart-header {
    padding: 1rem;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.cart-header h5 {
    margin: 0;
    font-size: 1rem;
    color: #333;
}

.cart-body {
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.cart-empty {
    text-align: center;
    padding: 1rem;
    color: #757575;
}

.cart-footer {
    padding: 1rem;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
}

.cart-total {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-weight: 600;
}

.view-cart-btn {
    display: block;
    width: 100%;
    text-align: center;
    padding: 0.5rem;
    background-color: #4051b5;
    color: white;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    text-decoration: none;
}

.view-cart-btn:hover {
    background-color: #3949ab;
    text-decoration: none;
    color: white;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem 0;
    animation: fadeIn 0.8s ease-out;
}

/* Cards */
.card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    height: 100%;
    border: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--secondary);
}

.card-text {
    color: var(--gray-dark);
    margin-bottom: 1rem;
}

.card-footer {
    background-color: var(--gray-light);
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1.25rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: var(--dark);
    border-color: var(--dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-accent {
    background-color: var(--accent);
    color: white;
    border-color: var(--accent);
}

.btn-accent:hover {
    background-color: #d35400;
    border-color: #d35400;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--radius-rounded);
    transition: var(--transition-fast);
}

.badge-primary {
    background-color: var(--primary);
    color: white;
}

.badge-secondary {
    background-color: var(--secondary);
    color: white;
}

.badge-accent {
    background-color: var(--accent);
    color: white;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-warning {
    background-color: var(--warning);
    color: white;
}

.badge-danger {
    background-color: var(--danger);
    color: white;
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--secondary);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    background-color: var(--gray-light);
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--secondary);
    background-color: white;
    background-clip: padding-box;
    border: 1px solid var(--gray);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.form-control:focus {
    color: var(--secondary);
    background-color: white;
    border-color: var(--primary);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(74, 111, 165, 0.25);
}

/* Footer */
.footer {
    background-color: var(--primary-dark);
    color: white;
    padding: 3rem 0;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.2) 100%);
    z-index: 1;
}

.footer-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section {
    padding: 0 1rem;
}

.footer-title {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
    padding-bottom: 0.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--accent);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-link {
    margin-bottom: 0.75rem;
    transition: var(--transition-fast);
}

.footer-link a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
}

.footer-link a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-link i {
    margin-right: 0.5rem;
    color: var(--accent);
    font-size: 0.9rem;
}

.social-icons {
    display: flex;
    margin-top: 1.5rem;
    gap: 0.75rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: var(--transition-fast);
    color: white;
}

.social-icon:hover {
    background-color: var(--accent);
    transform: translateY(-3px);
    color: white;
}

.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1.5rem 0;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    position: relative;
    z-index: 2;
    margin-top: 2rem;
}

/* Cart and Checkout Styles */
.cart-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-light);
    background-color: white;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
}

.cart-item:hover {
    box-shadow: var(--shadow-md);
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-sm);
    object-fit: cover;
    margin-right: 1.5rem;
    border: 1px solid var(--gray-light);
}

.cart-item-details {
    flex: 1;
}

.cart-item-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--secondary);
}

.cart-item-price {
    color: var(--primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--gray-light);
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.quantity-btn:hover {
    background-color: var(--primary);
    color: white;
}

.quantity-input {
    width: 40px;
    height: 30px;
    text-align: center;
    border: 1px solid var(--gray-light);
    border-radius: var(--radius-sm);
    margin: 0 0.5rem;
}

.cart-summary {
    background-color: white;
    border-radius: var(--radius-md);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 100px;
}

.cart-summary-title {
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-light);
    color: var(--secondary);
}

.cart-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    color: var(--secondary);
}

.cart-summary-total {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-light);
    font-weight: 600;
    font-size: 1.1rem;
}

.checkout-section {
    background-color: white;
    border-radius: var(--radius-md);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
}

.checkout-title {
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-light);
    color: var(--secondary);
}

.payment-option {
    cursor: pointer;
    margin-bottom: 1rem;
    display: block;
}

.payment-card {
    background-color: white;
    border: 1px solid var(--gray-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    transition: var(--transition-normal);
}

.payment-option input[type="radio"] {
    display: none;
}

.payment-option input[type="radio"]:checked + .payment-card {
    border-color: var(--primary);
    box-shadow: 0 0 0 1px var(--primary);
}

.payment-card:hover {
    box-shadow: var(--shadow-md);
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
}

/* Product Card Styles */
.product-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    border: none;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 200px;
    overflow: hidden;
    position: relative;
    background-color: #f5f5f5;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.5) 100%);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay .btn {
    transform: translateY(20px);
    transition: transform 0.3s ease, background-color 0.3s ease;
    margin-bottom: 1rem;
}

.product-card:hover .product-overlay .btn {
    transform: translateY(0);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: #ff5722; /* Orange accent color */
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.card-body {
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.card-title {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
    font-size: 1.1rem;
    line-height: 1.4;
}

.product-stats {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.rating {
    display: flex;
    align-items: center;
    color: #757575;
    font-size: 0.9rem;
}

.rating i {
    color: #ffc107; /* Yellow star color */
    margin-right: 0.25rem;
}

.rating-value {
    font-weight: 600;
    color: #333;
    margin-left: 0.5rem;
}

.product-price {
    color: #4051b5; /* Indigo color */
    font-weight: 700;
    font-size: 1.25rem;
    margin-top: auto;
}

.product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.add-to-cart-btn {
    padding: 0.5rem 1rem;
    background-color: #4051b5;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
}

.add-to-cart-btn:hover {
    background-color: #3949ab;
    text-decoration: none;
    color: white;
}

.add-to-cart-btn i {
    margin-right: 0.5rem;
}

/* Category Styles */

.product-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 60%);
    opacity: 0;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay .btn {
    transform: translateY(20px);
    transition: var(--transition-normal);
    opacity: 0;
}

.product-card:hover .product-overlay .btn {
    transform: translateY(0);
    opacity: 1;
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background-color: var(--accent);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-rounded);
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.product-details {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--secondary);
    font-size: 1.1rem;
    line-height: 1.4;
}

.product-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-light);
}

.stat {
    display: flex;
    align-items: center;
    color: var(--gray-dark);
    font-size: 0.9rem;
}

.stat i {
    color: var(--warning);
    margin-right: 0.5rem;
}

.stat-value {
    font-weight: 600;
    color: var(--secondary);
}

.product-price {
    color: var(--primary);
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: 0;
}

.product-description {
    color: var(--gray-dark);
    margin-bottom: 1.5rem;
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.6;
}

.product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

/* Flying item animation */
.flying-item {
    position: fixed;
    z-index: 9999;
    pointer-events: none;
    animation: fly-to-cart 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    box-shadow: 0 0 20px rgba(74, 111, 165, 0.7);
    border: 2px solid var(--primary);
}

@keyframes fly-to-cart {
    0% {
        transform: scale(0.7) translateX(0) translateY(0);
        opacity: 1;
    }
    70% {
        opacity: 1;
    }
    100% {
        transform: scale(0.1) translateX(var(--end-x)) translateY(var(--end-y));
        opacity: 0;
    }
}

/* Cart notification */
.cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--radius-md);
    padding: 15px;
    color: var(--secondary);
    z-index: 9999;
    box-shadow: var(--shadow-lg);
    transform: translateX(150%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    max-width: 300px;
    border: 1px solid var(--gray-light);
}

.cart-notification.show {
    transform: translateX(0);
}

.cart-notification-image {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
    margin-right: 15px;
    background-size: cover;
    background-position: center;
    border: 1px solid var(--gray-light);
}

.cart-notification-content {
    flex: 1;
}

.cart-notification-title {
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.cart-notification-message {
    font-size: 0.8rem;
    color: var(--secondary);
    margin: 0;
}

/* Category Styles */
.category-header {
    position: relative;
    margin-bottom: 1.5rem;
}

.category-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.75rem;
    position: relative;
    display: inline-block;
}

.category-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 4px;
    background: linear-gradient(to right, var(--primary), var(--accent));
    border-radius: 2px;
}

.category-divider {
    height: 1px;
    background: linear-gradient(to right, rgba(63, 81, 181, 0.2), transparent);
    margin-top: 0.5rem;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.category-card {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: var(--secondary);
    border: 1px solid rgba(63, 81, 181, 0.1);
    position: relative;
    overflow: hidden;
    font-weight: 500;
    text-decoration: none;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary), var(--accent));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    color: var(--primary);
    border-color: var(--primary);
    text-decoration: none;
}

.category-card:hover::before {
    opacity: 1;
}

.category-card i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 1rem;
    background-color: rgba(63, 81, 181, 0.1);
    color: var(--primary);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.category-card:hover i {
    transform: scale(1.1) rotate(5deg);
    background-color: var(--primary);
    color: white;
    box-shadow: 0 0 15px rgba(63, 81, 181, 0.5);
    animation: pulse-glow 1.5s infinite;
}

/* Active category styling */
.category-card.active {
    background-color: rgba(63, 81, 181, 0.05);
    border-color: var(--primary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.category-card.active::before {
    opacity: 1;
}

.category-card.active i {
    background-color: var(--primary);
    color: white;
}

/* Responsive styles */
@media (max-width: 992px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        text-align: center;
    }

    .site-title {
        margin-bottom: 1rem;
    }

    .user-menu {
        margin-top: 1rem;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .cart-item-image {
        margin-bottom: 1rem;
        margin-right: 0;
    }

    .cart-item-quantity {
        margin-top: 1rem;
        margin-right: 0;
    }

    .cart-summary {
        position: static;
        margin-top: 2rem;
    }
}

@media (max-width: 576px) {
    .product-grid {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .product-actions .btn {
        width: 100%;
    }
}

/* Login Page Styles */
:root {
    --primary-color: #4051b5;
    --primary-dark: #3949ab;
    --accent-color: #ff5722;
    --text-color: #333333;
    --text-light: #757575;
    --background-color: #f5f5f5;
    --card-background: #ffffff;
    --input-border: #e0e0e0;
    --input-focus: #4051b5;
    --success-color: #4caf50;
    --error-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --border-radius: 8px;
}

body.login-page {
    background-color: var(--background-color);
    font-family: 'Poppins', sans-serif;
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.login-container {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    padding: 2rem;
}

.login-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
    animation: fadeIn 0.8s ease-out;
}

.login-header {
    text-align: center;
    padding: 2rem 2rem 1.5rem;
}

.login-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    animation: float 4s ease-in-out infinite;
}

.login-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-light);
    font-size: 1rem;
    margin-bottom: 0;
}

.login-form {
    padding: 0 2rem 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-color);
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--input-focus);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(64, 81, 181, 0.25);
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: white;
    text-align: center;
    white-space: nowrap;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    margin-bottom: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-right: 10px;
    accent-color: var(--primary-color);
}

.form-check-label {
    margin-bottom: 0;
    color: var(--text-light);
}

.login-btn {
    display: block;
    width: 100%;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    color: white;
    background-color: var(--primary-color);
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn i {
    margin-right: 0.5rem;
}

.login-footer {
    text-align: center;
    margin-top: 1.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.login-footer a {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.login-footer a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.alert {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.alert-error {
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: var(--error-color);
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    color: var(--success-color);
}

.alert-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    margin-bottom: 0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

/* Responsive styles */
@media (max-width: 576px) {
    .login-container {
        padding: 1rem;
    }
    
    .login-card {
        box-shadow: none;
    }
    
    .login-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    .login-form {
        padding: 0 1.5rem 1.5rem;
    }
}

<?php
// <PERSON><PERSON><PERSON> to add sample menu items with the downloaded images

require_once 'restoran/dbcontroller.php';

$db = new DBController();

// Sample menu items for each category
$menuItems = [
    // <PERSON><PERSON><PERSON> (Food) - Category ID 1
    [
        'idkategori' => 1,
        'menu' => 'Classic Beef Burger',
        'gambar' => 'burger.jpg',
        'harga' => 15.99
    ],
    [
        'idkategori' => 1,
        'menu' => 'Creamy Pasta Carbonara',
        'gambar' => 'pasta.jpg',
        'harga' => 18.50
    ],
    [
        'idkategori' => 1,
        'menu' => 'Grilled Ribeye Steak',
        'gambar' => 'steak.jpg',
        'harga' => 28.99
    ],
    [
        'idkategori' => 1,
        'menu' => 'Margherita Pizza',
        'gambar' => 'pizza.jpg',
        'harga' => 16.75
    ],
    [
        'idkategori' => 1,
        'menu' => 'Fresh Garden Salad',
        'gambar' => 'salad.jpg',
        'harga' => 12.50
    ],
    [
        'idkategori' => 1,
        'menu' => 'Club Sandwich',
        'gambar' => 'sandwich.jpg',
        'harga' => 14.25
    ],
    [
        'idkategori' => 1,
        'menu' => 'Asian Rice Bowl',
        'gambar' => 'rice_bowl.jpg',
        'harga' => 13.99
    ],
    [
        'idkategori' => 1,
        'menu' => 'Chicken Curry',
        'gambar' => 'chicken_curry.jpg',
        'harga' => 17.50
    ],
    [
        'idkategori' => 1,
        'menu' => 'Spicy Noodles',
        'gambar' => 'noodles.jpg',
        'harga' => 15.25
    ],
    [
        'idkategori' => 1,
        'menu' => 'Grilled Salmon',
        'gambar' => 'grilled_fish.jpg',
        'harga' => 24.99
    ],
    
    // Minuman (Drinks) - Category ID 2
    [
        'idkategori' => 2,
        'menu' => 'Premium Coffee',
        'gambar' => 'coffee.jpg',
        'harga' => 4.50
    ],
    [
        'idkategori' => 2,
        'menu' => 'Fresh Orange Juice',
        'gambar' => 'juice.jpg',
        'harga' => 5.25
    ],
    [
        'idkategori' => 2,
        'menu' => 'Berry Smoothie',
        'gambar' => 'smoothie.jpg',
        'harga' => 6.75
    ],
    [
        'idkategori' => 2,
        'menu' => 'Green Tea',
        'gambar' => 'tea.jpg',
        'harga' => 3.50
    ],
    [
        'idkategori' => 2,
        'menu' => 'Sparkling Soda',
        'gambar' => 'soda.jpg',
        'harga' => 3.25
    ],
    [
        'idkategori' => 2,
        'menu' => 'Chocolate Milkshake',
        'gambar' => 'milkshake.jpg',
        'harga' => 7.50
    ],
    [
        'idkategori' => 2,
        'menu' => 'Fresh Lemonade',
        'gambar' => 'lemonade.jpg',
        'harga' => 4.75
    ],
    [
        'idkategori' => 2,
        'menu' => 'Iced Coffee',
        'gambar' => 'iced_coffee.jpg',
        'harga' => 5.50
    ],
    
    // Dessert - Category ID 3
    [
        'idkategori' => 3,
        'menu' => 'Chocolate Cake',
        'gambar' => 'chocolate_cake.jpg',
        'harga' => 8.99
    ],
    [
        'idkategori' => 3,
        'menu' => 'Vanilla Ice Cream',
        'gambar' => 'ice_cream.jpg',
        'harga' => 6.50
    ],
    [
        'idkategori' => 3,
        'menu' => 'New York Cheesecake',
        'gambar' => 'cheesecake.jpg',
        'harga' => 9.75
    ],
    [
        'idkategori' => 3,
        'menu' => 'Classic Tiramisu',
        'gambar' => 'tiramisu.jpg',
        'harga' => 8.50
    ],
    [
        'idkategori' => 3,
        'menu' => 'Fresh Fruit Tart',
        'gambar' => 'fruit_tart.jpg',
        'harga' => 7.99
    ],
    [
        'idkategori' => 3,
        'menu' => 'Fudge Brownie',
        'gambar' => 'brownie.jpg',
        'harga' => 6.75
    ],
    [
        'idkategori' => 3,
        'menu' => 'Crème Brûlée',
        'gambar' => 'pudding.jpg',
        'harga' => 8.25
    ],
    [
        'idkategori' => 3,
        'menu' => 'Glazed Donut',
        'gambar' => 'donut.jpg',
        'harga' => 3.99
    ]
];

echo "<h2>Adding Sample Menu Items...</h2>\n";
echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>\n";

$successCount = 0;
$totalCount = count($menuItems);

foreach ($menuItems as $item) {
    $sql = "INSERT INTO tblmenu (idkategori, menu, gambar, harga) VALUES (
        {$item['idkategori']}, 
        '{$item['menu']}', 
        '{$item['gambar']}', 
        {$item['harga']}
    )";
    
    try {
        $db->runSQL($sql);
        echo "<p>✓ Added: <strong>{$item['menu']}</strong> - \${$item['harga']}</p>\n";
        $successCount++;
    } catch (Exception $e) {
        echo "<p>✗ Failed to add: <strong>{$item['menu']}</strong> - Error: {$e->getMessage()}</p>\n";
    }
}

echo "</div>\n";
echo "<h3>Summary:</h3>\n";
echo "<p><strong>$successCount</strong> out of <strong>$totalCount</strong> menu items added successfully.</p>\n";

if ($successCount > 0) {
    echo "<p>Your restaurant menu is now populated with sample items and images!</p>\n";
    echo "<p><a href='restoran/index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Your Restaurant Website</a></p>\n";
}
?>

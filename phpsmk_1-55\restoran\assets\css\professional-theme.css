/* Professional Theme CSS */
:root {
    /* Color Palette */
    --primary: #3498db;
    --primary-dark: #2980b9;
    --secondary: #2c3e50;
    --accent: #e74c3c;
    --light: #ecf0f1;
    --dark: #34495e;
    --success: #2ecc71;
    --warning: #f39c12;
    --danger: #e74c3c;
    --info: #3498db;
    --gray: #95a5a6;
    --gray-light: #ecf0f1;
    --gray-dark: #7f8c8d;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #3498db, #2980b9);
    --gradient-secondary: linear-gradient(135deg, #2c3e50, #1a252f);
    --gradient-accent: linear-gradient(135deg, #e74c3c, #c0392b);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 15px 35px rgba(0, 0, 0, 0.2);

    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Open Sans', sans-serif;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-rounded: 50px;

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes shimmer {
    0% { background-position: -100% 0; }
    100% { background-position: 200% 0; }
}

/* Base Styles */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: var(--font-primary);
    color: var(--secondary);
    background-color: var(--light);
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--secondary);
    line-height: 1.3;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: 1rem;
    color: var(--secondary);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Header */
.header {
    background-color: white;
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

.header.scrolled {
    padding: 0.5rem 0;
    box-shadow: var(--shadow-lg);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.site-title a {
    color: var(--secondary);
    transition: var(--transition-fast);
}

.site-title a:hover {
    color: var(--primary);
}

/* Navigation */
.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-left: 1.5rem;
}

.nav-link {
    color: var(--secondary);
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
}

.user-menu-item {
    margin-left: 1rem;
}

.user-badge {
    display: flex;
    align-items: center;
    background-color: var(--light);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-rounded);
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.user-badge:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.user-badge i {
    margin-right: 0.5rem;
}

.cart-badge {
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--accent);
    color: white;
    font-size: 0.7rem;
    font-weight: 700;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem 0;
    animation: fadeIn 0.8s ease-out;
}

/* Cards */
.card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    height: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.card-text {
    color: var(--gray-dark);
    margin-bottom: 1rem;
}

.card-footer {
    background-color: var(--gray-light);
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1.25rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: var(--dark);
    border-color: var(--dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-accent {
    background-color: var(--accent);
    color: white;
    border-color: var(--accent);
}

.btn-accent:hover {
    background-color: #c0392b;
    border-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--radius-rounded);
    transition: var(--transition-fast);
}

.badge-primary {
    background-color: var(--primary);
    color: white;
}

.badge-secondary {
    background-color: var(--secondary);
    color: white;
}

.badge-accent {
    background-color: var(--accent);
    color: white;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-warning {
    background-color: var(--warning);
    color: white;
}

.badge-danger {
    background-color: var(--danger);
    color: white;
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--secondary);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    background-color: var(--gray-light);
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--secondary);
    background-color: white;
    background-clip: padding-box;
    border: 1px solid var(--gray);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.form-control:focus {
    color: var(--secondary);
    background-color: white;
    border-color: var(--primary);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Pagination */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: var(--radius-md);
}

.page-item {
    margin: 0 0.25rem;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary);
    background-color: white;
    border: 1px solid var(--gray);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.page-link:hover {
    z-index: 2;
    color: white;
    text-decoration: none;
    background-color: var(--primary);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.page-item.active .page-link {
    z-index: 3;
    color: white;
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Footer */
.footer {
    background-color: var(--secondary);
    color: white;
    padding: 3rem 0;
    margin-top: auto;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    margin-right: 2rem;
    margin-bottom: 2rem;
}

.footer-section:last-child {
    margin-right: 0;
}

.footer-title {
    color: white;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: var(--gray-light);
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary);
    padding-left: 5px;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 2rem;
}

.social-icons {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin: 0 0.5rem;
    color: white;
    transition: var(--transition-fast);
}

.social-icon:hover {
    background-color: var(--primary);
    transform: translateY(-5px);
}

/* Animations */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.float {
    animation: float 6s ease-in-out infinite;
}

/* Responsive */
@media (max-width: 992px) {
    .header-container {
        flex-direction: column;
        text-align: center;
    }

    .nav-menu {
        margin-top: 1rem;
        justify-content: center;
    }

    .nav-item {
        margin: 0 0.75rem;
    }

    .user-menu {
        margin-top: 1rem;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    .nav-menu {
        flex-wrap: wrap;
    }

    .nav-item {
        margin: 0.5rem;
    }

    .footer-section {
        flex: 100%;
        margin-right: 0;
    }
}

@media (max-width: 576px) {
    .user-menu {
        flex-wrap: wrap;
    }

    .user-menu-item {
        margin: 0.5rem;
    }
}

/* Custom Components */
.category-card {
    display: block;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: var(--transition-normal);
    border-left: 4px solid var(--primary);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-left-color: var(--accent);
}

.category-card i {
    margin-right: 0.75rem;
    color: var(--primary);
    transition: var(--transition-fast);
}

.category-card:hover i {
    color: var(--accent);
}

.product-card {
    position: relative;
    overflow: hidden;
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 1rem;
    opacity: 0;
    transition: var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.product-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: var(--gray-light);
    border-radius: var(--radius-md);
}

.product-price {
    font-weight: 700;
    color: var(--primary);
    font-size: 1.25rem;
}

.add-to-cart-btn {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-normal);
}

.add-to-cart-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.add-to-cart-btn:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.product-overlay .add-to-cart-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--radius-md);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transform: translateY(20px);
    opacity: 0;
    transition: var(--transition-normal);
}

.product-card:hover .product-overlay .add-to-cart-btn {
    transform: translateY(0);
    opacity: 1;
}

.cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 1rem;
    display: flex;
    align-items: center;
    z-index: 1050;
    transform: translateX(150%);
    transition: var(--transition-normal);
    max-width: 300px;
    border-left: 4px solid var(--primary);
}

.cart-notification.show {
    transform: translateX(0);
    animation: pulse 1s;
}

.cart-notification-image {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-sm);
    margin-right: 1rem;
    background-size: cover;
    background-position: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-light);
}

.cart-notification-content {
    flex: 1;
}

.cart-notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--primary);
}

.cart-notification-message {
    font-size: 0.875rem;
    color: var(--gray-dark);
    margin: 0;
}

/* Order History & Details */
.order-card {
    margin-bottom: 1.5rem;
}

.order-header {
    background-color: var(--gray-light);
    padding: 1.25rem;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.order-body {
    padding: 1.5rem;
}

.order-item {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-light);
}

.order-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.order-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-sm);
    margin-right: 1rem;
    background-size: cover;
    background-position: center;
}

.order-item-details {
    flex: 1;
}

.order-item-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.order-item-price {
    font-weight: 700;
    color: var(--primary);
}

.order-total {
    text-align: right;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-light);
}

.order-total-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.order-total-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

/* Checkout */
.payment-methods {
    margin-bottom: 2rem;
}

.payment-option {
    display: block;
    margin-bottom: 1rem;
}

.payment-card {
    background-color: white;
    border: 1px solid var(--gray-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    transition: var(--transition-normal);
    cursor: pointer;
}

.payment-card:hover {
    border-color: var(--primary);
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.payment-option input[type="radio"] {
    display: none;
}

.payment-option input[type="radio"]:checked + .payment-card {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary);
}

.payment-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
}

.payment-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.payment-description {
    font-size: 0.875rem;
    color: var(--gray-dark);
    margin: 0;
}

/* Animations for specific elements */
.site-title a {
    animation: pulse 2s infinite;
}

.btn-primary {
    position: relative;
    overflow: hidden;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn-primary:hover::after {
    left: 100%;
    animation: shimmer 1s;
}

/* Cart Item Styles */
.cart-item-row {
    transition: var(--transition-normal);
}

.cart-item-row:hover {
    background-color: var(--gray-light) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.quantity-control {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--gray-light);
    border-radius: var(--radius-rounded);
    padding: 5px;
    border: 1px solid var(--gray);
    width: fit-content;
    margin: 0 auto;
    box-shadow: var(--shadow-sm);
}

.quantity-control a {
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quantity-control a:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.item-quantity {
    font-weight: bold;
    color: var(--secondary);
    min-width: 20px;
    text-align: center;
    margin: 0 10px;
}

.item-subtotal {
    color: var(--primary);
    font-weight: 700;
    font-size: 1.1rem;
}

/* Cart Summary */
.order-summary {
    background-color: white;
    border: 1px solid var(--gray);
    border-radius: var(--radius-md);
    padding: 20px;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
}

.order-summary h4 {
    color: var(--primary);
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.order-summary h4 i {
    background: var(--gradient-primary);
    color: white;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover::after {
    left: 100%;
}

.card {
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: var(--transition-normal);
}

.card:hover::before {
    transform: scaleX(1);
}

.social-icon {
    position: relative;
    overflow: hidden;
}

.social-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: scale(0);
    transition: var(--transition-fast);
}

.social-icon:hover::before {
    transform: scale(2);
    opacity: 0;
}

<div class="menu-header">
    <div class="menu-title">
        <i class="fas fa-utensils"></i>
        <h2>Our Menu</h2>
    </div>
    <p class="menu-subtitle">Discover our delicious selection of premium products</p>
</div>

<div class="mt-4 mb-4">
    <?php
        if (isset($_GET['id'])) {
            $id = $_GET['id'];
            $where = "WHERE idkategori = $id";

            // Get category name
            $sqlCat = "SELECT kategori FROM tblkategori WHERE idkategori = $id";
            $rowCat = $db->getITEM($sqlCat);
            $catName = $rowCat['kategori'] ?? 'All Menu';

            // Create URL parameter after using $id in SQL
            $idParam = "&id=$id";

            echo '<div class="category-filter">
                <div class="category-filter-icon">
                    <i class="fas fa-filter"></i>
                </div>
                <div class="category-filter-text">
                    <span>Category:</span>
                    <strong>'.$catName.'</strong>
                </div>
            </div>';
        } else {
            $where = "";
            $id = "";

            echo '<div class="category-filter">
                <div class="category-filter-icon">
                    <i class="fas fa-th-large"></i>
                </div>
                <div class="category-filter-text">
                    <span>Showing:</span>
                    <strong>All Menu</strong>
                </div>
            </div>';
        }
    ?>
</div>

<?php
    $jumlahdata = $db->rowCOUNT("SELECT idmenu FROM tblmenu $where");
    $banyak = 3;

    $halaman = ceil($jumlahdata / $banyak);

    if (isset($_GET['p'])) {
        $p=$_GET['p'];
        $mulai = $p * $banyak - $banyak;

    }else {
        $mulai =0;
    }

    $sql = "SELECT * FROM tblmenu $where ORDER BY menu ASC LIMIT $mulai,$banyak";
    $row = $db->getALL($sql);

    $no=1+$mulai;
?>
<div class="row">
    <?php if(!empty($row)) { ?>
    <?php foreach($row as $r): ?>
        <div class="col-md-4 mb-4">
            <div class="menu-card">
                <div class="menu-card-image">
                    <?php
                        $imagePath = 'upload/' . $r['gambar'];
                        $defaultImage = 'upload/default.jpg';
                        $imageToShow = (file_exists($imagePath) && !empty($r['gambar'])) ? $imagePath : $defaultImage;
                    ?>
                    <img src="<?php echo $imageToShow; ?>" alt="<?php echo $r['menu']; ?>" onerror="this.src='upload/default.jpg'">
                    <?php if($no <= 3): ?>
                    <div class="menu-badge">
                        <i class="fas fa-utensils"></i>
                        <span>FEATURED</span>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="menu-card-content">
                    <h3 class="menu-card-title"><?php echo $r['menu']?></h3>
                    <div class="menu-card-rating">
                        <div class="rating-stars">
                            <?php
                                $rating = rand(4, 5);
                                for($i = 1; $i <= 5; $i++) {
                                    if($i <= $rating) {
                                        echo '<i class="fas fa-star"></i>';
                                    } else {
                                        echo '<i class="far fa-star"></i>';
                                    }
                                }
                            ?>
                        </div>
                        <div class="rating-text"><?php echo $rating; ?>/5</div>
                    </div>
                    <div class="menu-card-price">
                        <span>$<?php echo number_format($r['harga'], 2) ?></span>
                    </div>
                    <div class="menu-card-actions">
                        <a href="?f=home&m=beli&id=<?php echo $r['idmenu']?>" class="menu-card-button add-to-cart-btn"
                            data-product-id="<?php echo $r['idmenu']?>"
                            data-product-name="<?php echo $r['menu']?>"
                            data-product-image="<?php echo $r['gambar']?>"
                        >
                            <i class="fas fa-cart-plus"></i>
                            <span>Add to Cart</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach ?>
    <?php } else { ?>
        <div class="col-12">
            <div class="empty-menu">
                <div class="empty-menu-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h3 class="empty-menu-title">No Menu Available</h3>
                <p class="empty-menu-message">There are no menu items available in this category at the moment.</p>
                <a href="?f=home&m=produk" class="empty-menu-button">
                    <i class="fas fa-sync"></i>
                    <span>View All Menu</span>
                </a>
            </div>
        </div>
    <?php } ?>
</div>

<!-- Hidden table for reference, can be removed later -->
<table class="table table-ml d-none">
    <thead>
        <tr>
            <th>No</th>
            <th>Menu</th>
            <th>Harga</th>
            <th>Gambar</th>
        </tr>
    </thead>
    <tbody>
        <?php if(!empty($row)) { ?>
        <?php $counter = 1; ?>
        <?php foreach($row as $r): ?>
            <tr>
                <td><?php echo $counter++ ?></td>
                <td><?php echo $r['menu'] ?></td>
                <td><?php echo $r['harga'] ?></td>
                <td>
                    <?php
                        $imagePath = 'upload/' . $r['gambar'];
                        $defaultImage = 'upload/default.jpg';
                        $imageToShow = (file_exists($imagePath) && !empty($r['gambar'])) ? $imagePath : $defaultImage;
                    ?>
                    <img style="width:100px" src="<?php echo $imageToShow; ?>" alt="<?php echo $r['menu']; ?>" onerror="this.src='upload/default.jpg'">
                </td>
            </tr>
        <?php endforeach ?>
        <?php } ?>
    </tbody>
</table>

<div class="menu-pagination">
    <div class="menu-pagination-wrapper">
        <nav class="menu-pagination-nav">
            <?php
                // Current page
                $currentPage = isset($_GET['p']) ? $_GET['p'] : 1;

                // Previous button
                $prevDisabled = $currentPage > 1 ? '' : 'disabled';
                $prevPage = $currentPage - 1 > 0 ? $currentPage - 1 : 1;
            ?>

            <a href="?f=home&m=produk&p=<?php echo $prevPage.($idParam ?? ''); ?>" class="pagination-arrow <?php echo $prevDisabled; ?>" aria-label="Previous page">
                <i class="fas fa-chevron-left"></i>
            </a>

            <div class="pagination-numbers">
                <?php for ($i = 1; $i <= $halaman; $i++): ?>
                    <a href="?f=home&m=produk&p=<?php echo $i.($idParam ?? ''); ?>" class="pagination-number <?php echo ($currentPage == $i) ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
            </div>

            <?php
                // Next button
                $nextDisabled = ($currentPage < $halaman) ? '' : 'disabled';
                $nextPage = $currentPage + 1 <= $halaman ? $currentPage + 1 : $halaman;
            ?>

            <a href="?f=home&m=produk&p=<?php echo $nextPage.($idParam ?? ''); ?>" class="pagination-arrow <?php echo $nextDisabled; ?>" aria-label="Next page">
                <i class="fas fa-chevron-right"></i>
            </a>
        </nav>
    </div>
</div>

<style>
    /* Menu Header Styles */
    .menu-header {
        margin-bottom: 2rem;
        animation: fadeInDown 0.6s ease-out;
    }

    .menu-title {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.5rem;
    }

    .menu-title i {
        font-size: 2rem;
        color: #4e73df;
        background: rgba(78, 115, 223, 0.1);
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow: 0 4px 10px rgba(78, 115, 223, 0.2);
        animation: pulse 2s infinite;
    }

    .menu-title h2 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #333;
        margin: 0;
    }

    .menu-subtitle {
        color: #6c757d;
        margin-left: 4rem;
        font-size: 1rem;
    }

    /* Category Filter Styles */
    .category-filter {
        display: inline-flex;
        align-items: center;
        background: white;
        padding: 0.75rem 1.25rem;
        border-radius: 50px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        margin-bottom: 2rem;
        animation: slideInLeft 0.5s ease-out;
        transition: all 0.3s ease;
        border-left: 4px solid #4e73df;
    }

    .category-filter:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }

    .category-filter-icon {
        margin-right: 1rem;
        width: 35px;
        height: 35px;
        background: rgba(78, 115, 223, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4e73df;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .category-filter:hover .category-filter-icon {
        background: #4e73df;
        color: white;
        transform: rotate(15deg);
    }

    .category-filter-text span {
        display: block;
        font-size: 0.75rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .category-filter-text strong {
        font-size: 1rem;
        color: #333;
        font-weight: 600;
    }

    /* Menu Card Styles */
    .menu-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.4s ease;
        height: 100%;
        position: relative;
        animation: fadeInUp 0.8s ease-out;
    }

    .menu-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .menu-card-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .menu-card-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.5s ease;
    }

    .menu-card:hover .menu-card-image img {
        transform: scale(1.1);
    }

    .menu-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: #ff5722;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 10px rgba(255, 87, 34, 0.3);
        animation: pulse 2s infinite;
        z-index: 10;
    }

    .menu-card-content {
        padding: 1.5rem;
    }

    .menu-card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .menu-card:hover .menu-card-title {
        color: #4e73df;
    }

    .menu-card-rating {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #eee;
    }

    .rating-stars {
        display: flex;
        gap: 0.25rem;
    }

    .rating-stars i {
        color: #ffc107;
        transition: all 0.3s ease;
    }

    .menu-card:hover .rating-stars i {
        transform: rotate(15deg);
    }

    .rating-text {
        font-weight: 600;
        color: #333;
    }

    .menu-card-price {
        font-size: 1.5rem;
        font-weight: 700;
        color: #4e73df;
        margin-bottom: 1.25rem;
    }

    .menu-card-price span {
        position: relative;
        display: inline-block;
    }

    .menu-card-price span::before {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 0;
        height: 2px;
        background: #4e73df;
        transition: all 0.3s ease;
    }

    .menu-card:hover .menu-card-price span::before {
        width: 100%;
    }

    .menu-card-actions {
        display: flex;
        justify-content: center;
    }

    .menu-card-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        background: #4e73df;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        width: 100%;
        box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .menu-card-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background: #3a5ccc;
        transition: all 0.3s ease;
        z-index: -1;
        border-radius: 50px;
    }

    .menu-card-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(78, 115, 223, 0.4);
        color: white;
        text-decoration: none;
    }

    .menu-card-button:hover::before {
        width: 100%;
    }

    /* Empty Menu Styles */
    .empty-menu {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 2rem;
        text-align: center;
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        margin: 2rem 0;
        animation: fadeIn 0.8s ease-out;
    }

    .empty-menu-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1.5rem;
        width: 100px;
        height: 100px;
        background: #f8f9fa;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        animation: pulse 2s infinite;
    }

    .empty-menu-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.75rem;
    }

    .empty-menu-message {
        color: #6c757d;
        margin-bottom: 1.5rem;
        max-width: 500px;
    }

    .empty-menu-button {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        background: #4e73df;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
    }

    .empty-menu-button:hover {
        background: #3a5ccc;
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(78, 115, 223, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Pagination Styles */
    .menu-pagination {
        margin: 3rem 0;
        display: flex;
        justify-content: center;
        animation: fadeInUp 1s ease-out;
    }

    .menu-pagination-wrapper {
        background: white;
        border-radius: 50px;
        padding: 0.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }

    .menu-pagination-nav {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .pagination-arrow {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #f8f9fa;
        color: #4e73df;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .pagination-arrow:hover:not(.disabled) {
        background: #4e73df;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(78, 115, 223, 0.25);
    }

    .pagination-arrow.disabled {
        color: #adb5bd;
        cursor: not-allowed;
        opacity: 0.5;
    }

    .pagination-numbers {
        display: flex;
        gap: 0.5rem;
    }

    .pagination-number {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #f8f9fa;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .pagination-number:hover:not(.active) {
        background: rgba(78, 115, 223, 0.1);
        color: #4e73df;
        transform: translateY(-2px);
    }

    .pagination-number.active {
        background: #4e73df;
        color: white;
        box-shadow: 0 4px 8px rgba(78, 115, 223, 0.25);
    }

    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            box-shadow: 0 4px 10px rgba(78, 115, 223, 0.2);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 8px 15px rgba(78, 115, 223, 0.3);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 4px 10px rgba(78, 115, 223, 0.2);
        }
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .menu-title {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .menu-subtitle {
            margin-left: 0;
        }

        .menu-card-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .menu-card-button {
            width: 100%;
        }
    }

    @media (max-width: 576px) {
        .menu-card-rating {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .menu-card-button span {
            display: none;
        }

        .menu-card-button {
            width: 50px;
            height: 50px;
            padding: 0;
            border-radius: 50%;
        }

        .menu-card-button i {
            margin: 0;
            font-size: 1.25rem;
        }

        .pagination-number {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }
    }
</style>


/* CSS to hide PHP error messages */
.warning, .error, .notice, .undefined {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    position: absolute !important;
    z-index: -9999 !important;
    overflow: hidden !important;
    pointer-events: none !important;
}

/* Hide specific error messages */
div:contains("Undefined"),
div:contains("Warning"),
div:contains("array key"),
div:contains("kategori"),
div:contains("c:\\xampp") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    position: absolute !important;
    z-index: -9999 !important;
    overflow: hidden !important;
    pointer-events: none !important;
}

/* Additional styles to clean up the UI */
.main-content {
    position: relative;
    overflow: hidden;
}

/* Hide any error messages that might appear */
.main-content > div.row > div.col-md-3 > div {
    display: none !important;
}

/* Make sure the category list is visible */
.main-content > div.row > div.col-md-3 > h3,
.main-content > div.row > div.col-md-3 > div.category-list {
    display: block !important;
}

/* Fix cart display issues */
.cart-preview {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    margin-top: 0.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.cart-preview-header {
    padding: 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-preview-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.cart-preview-count {
    font-size: 14px;
    color: #666;
}

.cart-preview-body {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.cart-preview-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.cart-preview-item-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    background-size: cover;
    background-position: center;
    margin-right: 15px;
    border: 1px solid #eee;
}

.cart-preview-item-details {
    flex: 1;
}

.cart-preview-item-name {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
}

.cart-preview-item-price {
    font-size: 12px;
    color: #666;
}

.cart-preview-item-total {
    font-weight: 600;
    color: #4051b5;
    font-size: 14px;
}

.cart-preview-footer {
    padding: 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
}

.cart-preview-total {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-weight: 600;
    color: #333;
}

.cart-preview-empty {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.cart-preview-more {
    text-align: center;
    padding: 10px;
    font-size: 12px;
    color: #666;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-top: 10px;
}

/* Fix cart display in header */
.header .cart-badge {
    position: relative;
}

/* Hide unwanted text in cart */
.header .user-menu > div:not(.user-menu-item) {
    display: none !important;
}

/* Fix cart popup in header */
.header .user-menu-item {
    position: relative;
}

/* Fix cart display in main content */
.cart-header {
    background: var(--gradient-primary);
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
    border-bottom: 2px solid #3949ab;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Fix floating text in cart */
.header + div.container {
    display: none !important;
}

/* Fix cart display in main page */
.main-content {
    position: relative;
}

/* Hide any text that appears outside of proper containers */
body > div:not(.header):not(.main-content):not(.cart-notification):not(.notification-container) {
    display: none !important;
}

/* Fix cart item display */
.cart-item-row {
    background: rgba(30, 41, 59, 0.4) !important;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

/* Fix cart text in header */
.header .user-menu {
    display: flex;
    align-items: center;
}

/* Hide any direct text in header */
.header > div.container > div.header-container > div.user-menu > div:not(.user-menu-item) {
    display: none !important;
}

/* Fix cart popup */
.user-menu-item .cart-preview {
    display: none;
}

/* Fix cart display in main content */
.main-content .cart-header {
    position: relative;
    z-index: 1;
}

/* Hide any text that appears directly in body */
body > div:not(.header):not(.main-content):not(.cart-notification):not(.notification-container):not(.footer) {
    display: none !important;
}

/* Fix cart display in main page */
body > div.container {
    display: none !important;
}

/* Fix cart display in header */
.header .user-menu {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
}

/* Fix cart display in main content */
.main-content .cart-header {
    margin-bottom: 20px !important;
}

/* Fix cart item display */
.cart-item-row {
    background: rgba(30, 41, 59, 0.4) !important;
}

/* Fix cart item display */
.cart-item-row td {
    color: white !important;
}

/* Fix cart item display */
.cart-item-row .product-price {
    color: white !important;
}

/* Fix cart item display */
.cart-item-row .item-subtotal {
    color: white !important;
    font-weight: bold !important;
}

/* Fix cart item display */
.cart-item-row .quantity-control {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Fix cart item display */
.cart-item-row .quantity-control .btn {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Fix cart item display */
.cart-item-row .quantity-control .item-quantity {
    color: white !important;
    font-weight: bold !important;
}

/* Fix cart item image display */
.cart-item-row img {
    display: block !important;
    width: 60px !important;
    height: 60px !important;
    object-fit: cover !important;
    border-radius: 10px !important;
    margin: 0 auto !important;
    border: 2px solid #fbbf24 !important;
    box-shadow: 0 0 15px rgba(251, 191, 36, 0.3) !important;
}

/* Fix cart item name display */
.cart-item-row td:nth-child(2) div,
.product-name {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
    overflow: visible !important;
    position: static !important;
    z-index: 1 !important;
    pointer-events: auto !important;
}

/* Fix cart text in header */
.header .user-menu {
    position: relative;
}

/* Fix cart text in header */
.header .user-menu > div.user-menu.float-end.mt-3.animate__animated.animate__fadeInRight {
    display: flex !important;
    float: none !important;
    margin-top: 0 !important;
}
